/* eslint-disable @typescript-eslint/no-explicit-any */
import { logout } from '@/slice/authSlice';
import { toast } from 'sonner';
import { triggerCrossTabLogout } from './CrossTabHelper';

export const toAbsoluteUrl = (pathname: string) =>
  import.meta.env.BASE_URL + pathname;

export function checkTokenExpiration(
  dispatch: any,
  expirationTime: number | null,
) {
  if (expirationTime && Date.now() > expirationTime) {
    dispatch(logout());
  }
}

export function setupAxios(axios: any, dispatch: any, store: any) {
  axios.defaults.headers.Accept = 'application/json';
  axios.interceptors.request.use(
    (config: { headers: { Authorization: string } }) => {
      const { auth } = store.getState();
      if (auth && auth?.apiToken) {
        config.headers.Authorization = `Bearer ${auth?.apiToken}`;
      }
      return config;
    },
    (err: any) => Promise.reject(err),
  );

  axios.interceptors.response.use(
    function (response: any) {
      return response;
    },
    function (error: any) {
      let errorMessage = '',
        api = '';
      const errObject = error.response;
      const errRequest = errObject?.request;
      console.log(errRequest?.responseURL);

      // Check for JWT expired message
      if (errObject?.data?.message === 'jwt expired') {
        const newAlert = {
          id: new Date().getTime(),
          show: true,
          heading: 'Session Expired',
          message: 'Your session has expired. Please login again.',
          type: 'error',
          errMessage: '',
          errDescription: '',
        };

        toast.error(newAlert.message);
        dispatch(logout());
        triggerCrossTabLogout();
        return Promise.reject(error);
      }

      if (error?.response?.status === 404) {
        const newAlert = {
          id: new Date().getTime(),
          show: true,
          heading: 'No Data Found',
          message: '',
          type: 'warning',
          errMessage: '',
          errDescription: errObject?.data?.message,
        };

        toast.error(newAlert.message);
        return Promise.reject(error);
      }

      if (
        error.response?.status === 401 &&
        error.response?.statusText === 'Unauthorized' &&
        window.location.pathname !== '/auth/login'
      ) {
        const newAlert = {
          id: new Date().getTime(),
          show: true,
          heading: 'Session Expired',
          message: 'Your session has expired. Please login again.',
          type: 'error',
          errMessage: '',
          errDescription: '',
        };

        toast.error(newAlert.message);
        dispatch(logout());
        triggerCrossTabLogout();
        return Promise.reject(error);
      }

      if (errRequest) {
        const endPoint = errRequest.responseURL?.split('api');

        if (error.message) {
          toast.error(error.message);
          return Promise.reject(error);
        }

        if (errRequest.statusText) errorMessage += ' ' + errRequest.statusText;

        if (errRequest.status)
          errorMessage += '(Error Code:' + errRequest.status + ')';

        if (errObject.config.method)
          api +=
            errObject.config.method?.toUpperCase() +
            ' api ' +
            endPoint[1] +
            ' failed';
        const newAlert = {
          show: true,
          heading: 'Error',
          message: api,
          errMessage: errorMessage,
          errDescription: errObject?.data?.message,
          type: 'error',
        };

        toast.error(newAlert.message);
        return Promise.reject(error);
      }

      toast.error(error.message);

      return Promise.reject(error);
    },
  );
}
