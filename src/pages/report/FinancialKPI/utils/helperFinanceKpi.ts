export const regionSplitRowOrderTable = [
  { label: 'PM Revenue', key: 'PM Fees', unit: '' },
  { label: 'ROR', key: 'ROR', unit: '%' },
  { label: 'Fee/Unit', key: 'Fees/Unit', unit: '' },
  { label: 'Cost/Unit', key: 'Cost/Unit', unit: '' },
  { label: 'WAU', key: 'WAU', unit: '' },
  { label: 'Actual Units', key: 'Actual Units', unit: '' },
];

export function formatValue(
  value: number | undefined | null,
  unit: string,
  forExcel: boolean = false,
) {
  if (value === undefined || value === null) return '-';

  const rounded = Math.round(value);

  if (forExcel) {
    if (unit === '%') {
      // return rounded / 100;
      return value / 100;
    }
    return rounded;
  }

  if (unit === '$') {
    if (rounded < 0) {
      return `($${Math.abs(rounded).toLocaleString('en-US')})`;
    } else {
      return `$${rounded.toLocaleString('en-US')}`;
    }
  }

  // if (unit === '%') return `${(value ?? 0).toFixed(1)}%`;
  if (unit === '%') {
    if (value < 0) {
      return `(${(Math.abs(value) ?? 0).toFixed(1)}%)`;
    } else {
      return `${(value ?? 0).toFixed(1)}%`;
    }
  }

  if (rounded < 0) {
    return `(${Math.abs(rounded).toLocaleString('en-US')})`;
  } else {
    return `${rounded.toLocaleString('en-US')}`;
  }
}
