import React, { useEffect, useMemo, useState } from 'react';
import {
  unitWalkKPIRegionApi,
  unitWalkKPIYTDMarketApi,
} from '@/api/unitWalkApis/unitWalkApis';
import {
  UnitWalkKPIPayload,
  UnitWalkKPIRegionTypes,
  UnitWalkKPIYTDMarketTypes,
} from '@/api/unitWalkApis/unitWalkTypes';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { Container } from '@/components/common/container';
import ReportFilters from '../IncomeReport/components/ReportFilters';
import {
  ReportsTabsList,
  ReportTabsContentUILayout,
  ReportTabsTrigger,
} from '../ReportsCommonComponents/reportsTabsUi/ReportsTabsAtoms';
import UnitWalkRegionTable from './components/UnitWalkRegionTable';
import UnitWalkYTDMarketTable from './components/UnitWalkYTDMarketTable';

const UnitWalkPage: React.FC = () => {
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const [loading1, setLoading] = useState({
    region: false,
    market: false,
  });
  const [error1, setError] = useState({
    region: '',
    market: '',
  });

  const [unitWalkRegionData, setUnitWalkRegionData] = useState<
    UnitWalkKPIRegionTypes[]
  >([]);
  const [unitWalkMarketData, setUnitWalkMarketData] = useState<
    UnitWalkKPIYTDMarketTypes[]
  >([]);

  const fetchUnitWalkRegion = async (requestBody: UnitWalkKPIPayload) => {
    setLoading((prev) => ({ ...prev, region: true }));
    setError((prev) => ({ ...prev, region: '' }));
    try {
      const res = await unitWalkKPIRegionApi(requestBody);
      setUnitWalkRegionData(res.data);
    } catch {
      setError((prev) => ({ ...prev, region: 'Failed to fetch region data' }));
    } finally {
      setLoading((prev) => ({ ...prev, region: false }));
    }
  };

  const fetchUnitWalkMarket = async (requestBody: UnitWalkKPIPayload) => {
    setLoading((prev) => ({ ...prev, market: true }));
    setError((prev) => ({ ...prev, market: '' }));
    try {
      const res = await unitWalkKPIYTDMarketApi(requestBody);
      setUnitWalkMarketData(res.data);
    } catch {
      setError((prev) => ({ ...prev, market: 'Failed to fetch market data' }));
    } finally {
      setLoading((prev) => ({ ...prev, market: false }));
    }
  };

  const getMonthNumber = (monthName: string): string => {
    const months = {
      January: '1',
      February: '2',
      March: '3',
      April: '4',
      May: '5',
      June: '6',
      July: '7',
      August: '8',
      September: '9',
      October: '10',
      November: '11',
      December: '12',
    };

    return months[monthName as keyof typeof months] || '1';
  };

  useEffect(() => {
    const monthNumbers =
      filters.month.length > 0
        ? filters.month.map((month) => getMonthNumber(month))
        : ['1'];
    const yearValue = filters.year || '2025';
    const requestBody = {
      year: yearValue,
      month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
      department:
        filters.department.length === 0
          ? null
          : filters.department.length === 1
            ? filters.department[0]
            : filters.department,
      businessType:
        filters.businessType.length === 0
          ? null
          : filters.businessType.length === 1
            ? filters.businessType[0]
            : filters.businessType,
      marketleader:
        filters.marketLeader.length === 0
          ? null
          : filters.marketLeader.length === 1
            ? filters.marketLeader[0]
            : filters.marketLeader,
      adminBu:
        filters.adminBU.length === 0
          ? null
          : filters.adminBU.length === 1
            ? filters.adminBU[0]
            : filters.adminBU,
    };

    fetchUnitWalkRegion(requestBody);
    fetchUnitWalkMarket(requestBody);
  }, [filters]);

  const datePeriod = useMemo(() => {
    const year = filters.year || '';
    const months = filters.month || [];

    if (months.length === 1) {
      return `${months[0]} ${year}`;
    } else if (months.length > 1) {
      return `${months[0]} - ${months[months.length - 1]} ${year}`;
    } else {
      return year;
    }
  }, [filters]);

  return (
    <Container title="Unit Walk" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>

        <div className="mt-10">
          <Tabs defaultValue="unitWalkRegion">
            <ReportsTabsList className="grid-cols-2">
              <ReportTabsTrigger
                value="unitWalkRegion"
                tabName={'Unit Walk Region'}
              />
              <ReportTabsTrigger
                value="unitWalkMarket"
                tabName={'YTD Unit Walk Market'}
              />
            </ReportsTabsList>

            <TabsContent value="unitWalkRegion">
              <ReportTabsContentUILayout
                loading={loading1.region}
                error={error1.region}
                component={
                  <UnitWalkRegionTable
                    regionData={unitWalkRegionData}
                    datePeriod={datePeriod}
                    filters={filters}
                  />
                }
              />
            </TabsContent>
            <TabsContent value="unitWalkMarket">
              <ReportTabsContentUILayout
                loading={loading1.market}
                error={error1.market}
                component={
                  <UnitWalkYTDMarketTable
                    marketData={unitWalkMarketData}
                    datePeriod={datePeriod}
                    filters={filters}
                  />
                }
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Container>
  );
};

export default UnitWalkPage;
