import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Regional data interface for occupancy and rents
export interface RegionalOccupancyData {
  actualVsBudget: string;
  actualVsSubmarket?: string;
  actualVsMarket?: string;
}

// Data interface for Occupancy & Rents tab
export interface OccupancyRentsItem {
  category: string;
  central: RegionalOccupancyData;
  east: RegionalOccupancyData;
  west: RegionalOccupancyData;
  consolidated: RegionalOccupancyData;
}

// Regional data interface for operational KPIs
export interface RegionalOperationalData {
  actual: string | number;
  target: string | number;
}

// Data interface for Operational KPIs tab
export interface OperationalKpiItem {
  category: string;
  central: RegionalOperationalData;
  east: RegionalOperationalData;
  west: RegionalOperationalData;
  consolidated: RegionalOperationalData;
}

// Data interface for Property Performance tab
export interface PropertyPerformanceItem {
  lineItem: string;
  central: string;
  east: string;
  west: string;
  consolidated: string;
}

// Filters interface (compatible with ReportFilters)
export interface PropertyManagementKpiFilters {
  year: string;
  month: string[];
  department: string[];
  businessType: string[];
  marketLeader: string[];
  adminBU: string[];
}

// Loading states for each section
export interface SectionLoadingStates {
  occupancyRents: boolean;
  operationalKpis: boolean;
  propertyPerformance: boolean;
}

// Error states for each section
export interface SectionErrorStates {
  occupancyRents: string | null;
  operationalKpis: string | null;
  propertyPerformance: string | null;
}

// Main state interface
export interface PropertyManagementKpiState {
  activeTab: 'occupancyRents' | 'operationalKpis' | 'propertyPerformance';
  filters: PropertyManagementKpiFilters;
  occupancyRentsData: OccupancyRentsItem[];
  operationalKpisData: OperationalKpiItem[];
  propertyPerformanceData: PropertyPerformanceItem[];
  loading: boolean; // Global loading state for backward compatibility
  error: string | null; // Global error state for backward compatibility
  sectionLoading: SectionLoadingStates;
  sectionErrors: SectionErrorStates;
}

// Initial state with defaults
const initialState: PropertyManagementKpiState = {
  activeTab: 'occupancyRents',
  filters: {
    year: '2025',
    month: ['January'],
    department: [],
    businessType: [],
    marketLeader: [],
    adminBU: [],
  },
  occupancyRentsData: [],
  operationalKpisData: [],
  propertyPerformanceData: [],
  loading: false,
  error: null,
  sectionLoading: {
    occupancyRents: false,
    operationalKpis: false,
    propertyPerformance: false,
  },
  sectionErrors: {
    occupancyRents: null,
    operationalKpis: null,
    propertyPerformance: null,
  },
};

// Create slice
const propertyManagementKpiSlice = createSlice({
  name: 'propertyManagementKpi',
  initialState,
  reducers: {
    setActiveTab: (
      state,
      action: PayloadAction<
        'occupancyRents' | 'operationalKpis' | 'propertyPerformance'
      >,
    ) => {
      state.activeTab = action.payload;
    },
    setFilters: (
      state,
      action: PayloadAction<Partial<PropertyManagementKpiFilters>>,
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setOccupancyRentsData: (
      state,
      action: PayloadAction<OccupancyRentsItem[]>,
    ) => {
      state.occupancyRentsData = action.payload;
    },
    setOperationalKpisData: (
      state,
      action: PayloadAction<OperationalKpiItem[]>,
    ) => {
      state.operationalKpisData = action.payload;
    },
    setPropertyPerformanceData: (
      state,
      action: PayloadAction<PropertyPerformanceItem[]>,
    ) => {
      state.propertyPerformanceData = action.payload;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setSectionLoading: (
      state,
      action: PayloadAction<Partial<SectionLoadingStates>>,
    ) => {
      state.sectionLoading = { ...state.sectionLoading, ...action.payload };
    },
    setSectionError: (
      state,
      action: PayloadAction<Partial<SectionErrorStates>>,
    ) => {
      state.sectionErrors = { ...state.sectionErrors, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
    clearSectionErrors: (state) => {
      state.sectionErrors = {
        occupancyRents: null,
        operationalKpis: null,
        propertyPerformance: null,
      };
    },
  },
});

// Export actions
export const {
  setActiveTab,
  setFilters,
  setOccupancyRentsData,
  setOperationalKpisData,
  setPropertyPerformanceData,
  setLoading,
  setError,
  setSectionLoading,
  setSectionError,
  resetFilters,
  clearSectionErrors,
} = propertyManagementKpiSlice.actions;

// Export reducer
export default propertyManagementKpiSlice.reducer;
