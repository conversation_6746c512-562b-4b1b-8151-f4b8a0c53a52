import ExcelJS from 'exceljs';

export const excelPurpleColor = { argb: 'FF43298F' };
export const excelLightPurpleColor = { argb: 'FFF4F4FF' };
export const excelLightBlueColor = { argb: 'FFDEEFFF' };
export const excelWhiteColor = { argb: 'FFFFFFFF' };
export const excelBlackColor = { argb: 'FF000000' };

// export const excelNumberFormateCommonSign = '#,##0;(#,##0)';
const numberUSFormate = '#,###;(#,###);"0"';
export const excelNumberFormateCommonSign = numberUSFormate;
export const excelNumberFormatePercenatgeSign = '0.0%;(0.0%)';
export const excelNumberFormateDollarSign = `"$"${numberUSFormate};("$"${numberUSFormate})`;

export const companyNameRowFont = {
  bold: true,
  size: 16,
  color: excelPurpleColor,
};
export const reportTitleRowFont = {
  bold: true,
  size: 14,
  color: excelPurpleColor,
};
export const filterPeriodRowFont = {
  bold: true,
  size: 12,
  color: excelPurpleColor,
};
export const filterConsolidationRowFont = {
  bold: true,
  size: 12,
  color: excelPurpleColor,
};

export const headingFill = {
  type: 'pattern' as const,
  pattern: 'solid' as const,
  fgColor: excelPurpleColor,
};

export const headingFillMergeFont = {
  bold: true,
  color: excelWhiteColor,
  size: 12,
};

export const columnHeadingFillFont = {
  bold: true,
  color: excelWhiteColor,
  size: 11,
};

export const rowColumnHeadingHeight = 35;

//test alignment
export const alignmentCenter = {
  horizontal: 'center' as const,
  vertical: 'middle' as const,
  wrapText: true,
};
export const alignmentRight = {
  horizontal: 'right' as const,
  vertical: 'middle' as const,
  wrapText: true,
};
export const alignmentLeft = {
  horizontal: 'left' as const,
  vertical: 'middle' as const,
  wrapText: true,
};

export const bodyRowCellFullBorders = {
  top: { style: 'thin' as const, color: excelLightBlueColor },
  right: { style: 'thin' as const, color: excelLightBlueColor },
  bottom: { style: 'thin' as const, color: excelLightBlueColor },
  left: { style: 'thin' as const, color: excelLightBlueColor },
};

export const bodyRowCellFill = {
  type: 'pattern' as const,
  pattern: 'solid' as const,
  fgColor: excelLightPurpleColor,
};

export const excelBorderThin = { style: 'thin' as ExcelJS.BorderStyle };

const mergeCellHeadingBorders = {
  style: 'thin' as ExcelJS.BorderStyle,
  color: excelBlackColor,
};
export const mainHeadingCellBorders = {
  top: mergeCellHeadingBorders,
  left: mergeCellHeadingBorders,
  bottom: mergeCellHeadingBorders,
  right: mergeCellHeadingBorders,
};
