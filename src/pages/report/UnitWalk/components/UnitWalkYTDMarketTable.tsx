import { UnitWalkKPIYTDMarketTypes } from '@/api/unitWalkApis/unitWalkTypes';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableHeadingCell,
  CommonTableMainHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelUnitWalkYTDMarket } from '../utils/exportDownloadFormattersUnitWalk';

interface UnitWalkYTDMarketTableProps {
  marketData: UnitWalkKPIYTDMarketTypes[];
  datePeriod: string;
  filters: ReportFilters;
}
const columnWidth = 'w-[140px]';

export default function UnitWalkYTDMarketTable(
  props: UnitWalkYTDMarketTableProps,
) {
  const { marketData, datePeriod, filters } = props;
  const exportUnitWalkMarket = () => {
    downloadExcelUnitWalkYTDMarket(marketData, filters);
  };
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={exportUnitWalkMarket}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <div>
        <CommonTable>
          <thead>
            <CommonTableMainHeaderRow>
              <CommonTableHeadingCell
                className={`${columnWidth}`}
                textAlign="text-start"
              >
                Market
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Beginning
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Additions
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Losses
              </CommonTableHeadingCell>
              <CommonTableHeadingCell className={`${columnWidth}`}>
                Current
              </CommonTableHeadingCell>
            </CommonTableMainHeaderRow>
          </thead>
          <tbody>
            {marketData?.map((marketItem) => {
              return (
                <CommonTableBodyRow
                  key={marketItem?.RegionMarket}
                  lastRowBorder={false}
                >
                  <CommonTableBodyCell
                    subHeading={false}
                    textAlign="text-start"
                  >
                    {marketItem?.RegionMarket}
                  </CommonTableBodyCell>
                  <CommonTableBodyCell>
                    {marketItem?.beginning?.toLocaleString('en-Us')}
                  </CommonTableBodyCell>

                  <CommonTableBodyCell>
                    {marketItem?.additions?.toLocaleString('en-US')}
                  </CommonTableBodyCell>
                  <CommonTableBodyCell>
                    {marketItem?.losses?.toLocaleString('en-US')}
                  </CommonTableBodyCell>
                  <CommonTableBodyCell>
                    {marketItem?.current?.toLocaleString('en-US')}
                  </CommonTableBodyCell>
                </CommonTableBodyRow>
              );
            })}
          </tbody>
        </CommonTable>
      </div>
    </div>
  );
}
