import { FinanceKPIRegionWiseResponce } from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import {
  alignmentCenter,
  alignmentLeft,
  alignmentRight,
  bodyRowCellFill,
  bodyRowCellFullBorders,
  columnHeadingFillFont,
  companyNameRowFont,
  excelBlackColor,
  excelNumberFormateCommonSign,
  excelNumberFormateDollarSign,
  excelNumberFormatePercenatgeSign,
  filterConsolidationRowFont,
  filterPeriodRowFont,
  headingFill,
  headingFillMergeFont,
  mainHeadingCellBorders,
  reportTitleRowFont,
  rowColumnHeadingHeight,
} from '@/constants/exportExcelStyles';
import { getConsolidationHeader } from '@/helpers/exportExcelGetConsolidationHeader';
import { ReportFilters } from '@/slice/incomeReportSlice';
import ExcelJS from 'exceljs';
import { toast } from 'sonner';
import { FinacialMarketTableDataFormatedTypes } from '../FinancialTables/MarketSplitTable';
import { formatValue, regionSplitRowOrderTable } from './helperFinanceKpi';

const borderThin = { style: 'thin' as ExcelJS.BorderStyle };
const tableColumnGap = 4;

// The object mapping each FeesType to its KPIValue
type RegionKPIMap = {
  [feesType: string]: FinanceKPIRegionWiseResponce;
};

// The grouped structure — each item is a tuple: [RegionName, RegionKPIMap]
type GroupedData = [string, RegionKPIMap][];

export const exportRegionWiseSplitPropertyToExcel = async (
  groupedData: GroupedData,
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Finance KPI Region-Split YTD');

  let displayMonth = '';
  const currentYear = filters.year || '2025';

  if (filters?.month?.length) {
    if (filters.month.length === 1) {
      displayMonth = filters.month[0];
    } else {
      displayMonth =
        filters.month[0] + ' - ' + filters.month[filters.month.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:G1');

  const titleRow = worksheet.addRow(['Region Split- YTD and Full Year']);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:G2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:G3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:G4');

  worksheet.addRow([]);
  const groupHeaderRow = worksheet.addRow([
    '',

    'YTD Act. Vs. Budget',
    '',
    '',

    '', //gap

    'Full Year Exp. Vs. Budget',
    '',
    '',
  ]);

  groupHeaderRow.eachCell((cell, colNum) => {
    if (colNum === 2 || colNum === 6) {
      cell.fill = headingFill;
      cell.font = headingFillMergeFont;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingCellBorders;
    }
  });
  const lastRow = worksheet?.lastRow?.number;
  worksheet.mergeCells(`B${lastRow}:D${lastRow}`);
  worksheet.mergeCells(`F${lastRow}:H${lastRow}`);

  // Set column widths
  worksheet.columns = [
    { width: 20 }, // Label
    { width: 15 }, // Actuals
    { width: 15 }, // Budget
    { width: 15 }, // Variance

    { width: tableColumnGap }, // gap

    { width: 15 }, // Expected
    { width: 15 }, // Budget1
    { width: 15 }, // Variance1
  ];

  groupedData.forEach(([region, feesMap]) => {
    // Sub headers row
    const subHeader = worksheet.addRow([
      region?.toUpperCase(),
      'Actual',
      'Budget',
      'Variance',
      '', //gap
      'Expected',
      'Budget',
      'Variance',
    ]);
    subHeader.height = rowColumnHeadingHeight;
    subHeader.eachCell((cell, colNum) => {
      if (colNum !== 1 && colNum !== 5) {
        cell.fill = headingFill;
        cell.font = columnHeadingFillFont;
        cell.border = mainHeadingCellBorders;
        cell.alignment = alignmentCenter;
      } else if (colNum === 1) {
        cell.font = { bold: true, underline: true, size: 11 };
        cell.alignment = { vertical: 'middle' };
      }
    });

    // Data rows
    regionSplitRowOrderTable.forEach(({ label, key, unit }, index) => {
      const item = feesMap?.[key] || {};

      const rowCells = worksheet.addRow([
        label,
        formatValue(item?.Actual, unit, true),
        formatValue(item.budget, unit, true),
        formatValue(item?.Variance, unit, true),
        '', //gap
        formatValue(item?.forecast, unit, true),
        formatValue(item?.budget1, unit, true),
        formatValue(item?.Variance1, unit, true),
      ]);

      rowCells.eachCell((cell, colNum) => {
        if (unit === '$') cell.numFmt = excelNumberFormateDollarSign;
        else if (unit === '%') cell.numFmt = excelNumberFormatePercenatgeSign;
        else cell.numFmt = excelNumberFormateCommonSign;

        if (colNum !== 1) {
          cell.alignment = alignmentRight;
        } else {
          cell.alignment = alignmentLeft;
        }

        if (index % 2 === 0) {
          cell.fill = bodyRowCellFill;
          cell.border = bodyRowCellFullBorders;
        }

        if (colNum === 1 || colNum === 4 || colNum === 5 || colNum === 8) {
          cell.border = {
            right: { style: 'thin', color: excelBlackColor },
          };
        }

        if (index === regionSplitRowOrderTable.length - 1) {
          if (colNum !== 1 && colNum !== 5) {
            cell.border = {
              ...cell.border,
              bottom: { style: 'thin', color: excelBlackColor },
            };
          }
        }
      });
    });
  });

  const displayMonthYear = `${filters.month?.[0] ?? 'January'} ${currentYear}`;
  // Write to Excel file
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Region_Split_YTD_${displayMonthYear.replace(' ', '_')}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url);
  toast.success('Excel export successful');
};

export const downloadExcelYTDFullYear = async (
  summaryData: FinanceKPIRegionWiseResponce[],
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(
    'YTD and Full Year Expected Actual VS Budget',
  );

  let displayMonth = '';
  const currentYear = filters.year || '2025';

  if (filters?.month?.length) {
    if (filters.month.length === 1) {
      displayMonth = filters.month[0];
    } else {
      displayMonth =
        filters.month[0] + ' - ' + filters.month[filters.month.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:G1');

  const titleRow = worksheet.addRow([
    'YTD and Full Year Expected Actual VS Budget',
  ]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:G2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:G3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:G4');

  worksheet.addRow(['']);
  worksheet.addRow(['']);

  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:D${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `F${worksheet.lastRow?.number}:H${worksheet.lastRow?.number}`,
  );

  worksheet.getCell(`B${worksheet.lastRow?.number}`).value = 'YTD vs. Budget';
  worksheet.getCell(`F${worksheet.lastRow?.number}`).value =
    'Full Year Expected vs. Budget';

  worksheet.getCell(`B${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`F${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;

  worksheet.getCell(`B${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`F${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`F${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`F${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;

  const subHeadingsRow = [
    ' ',
    'Actual',
    'Budget',
    'Variance',

    '', // gap

    // Budget
    'Expected',
    'Budget',
    'Variance',
  ];

  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = rowColumnHeadingHeight;

  worksheet.columns = [
    { width: 18 },
    { width: 15 },
    { width: 15 },
    { width: 15 },

    { width: tableColumnGap }, //gap

    { width: 15 },
    { width: 15 },
    { width: 15 },
  ];

  subheadings.eachCell((cell, colNum) => {
    if (colNum !== 1 && colNum !== 5) {
      cell.fill = headingFill;
      cell.font = columnHeadingFillFont;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingCellBorders;
    }
  });

  regionSplitRowOrderTable.forEach(({ label, key, unit }, index) => {
    const item = summaryData?.find((item) => item?.FeesType === key);

    const rowCells = worksheet.addRow([
      label,
      formatValue(item?.Actual, unit, true),
      formatValue(item?.budget, unit, true),
      formatValue(item?.Variance, unit, true),

      '', //gap

      formatValue(item?.forecast, unit, true),
      formatValue(item?.budget1, unit, true),
      formatValue(item?.Variance1, unit, true),
    ]);

    if (index % 2 === 0) {
      rowCells.eachCell((cell) => {
        cell.fill = bodyRowCellFill;
        cell.border = bodyRowCellFullBorders;
      });
    }

    rowCells.eachCell((cell, colNum) => {
      if (unit === '$') cell.numFmt = excelNumberFormateDollarSign;
      else if (unit === '%') cell.numFmt = excelNumberFormatePercenatgeSign;
      else cell.numFmt = excelNumberFormateCommonSign;

      if (colNum !== 1) {
        cell.alignment = alignmentRight;
      } else {
        cell.alignment = alignmentLeft;
      }

      if (colNum === 1 || colNum === 4 || colNum === 5 || colNum === 8) {
        cell.border = {
          ...cell.border,
          right: { style: 'thin' },
        };
      }

      if (index === regionSplitRowOrderTable.length - 1) {
        if (colNum !== 1 && colNum !== 5) {
          cell.border = {
            ...cell.border,
            bottom: { style: 'thin' },
          };
        }
      }
    });
  });

  const displayMonthYear = `${filters.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `YTD_Full_Year_${displayMonthYear.replace(' ', '_')}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

const marketTableColumsWidth = [
  { width: 25 },

  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },

  { width: tableColumnGap }, // gap

  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },

  { width: 15 },
  { width: 16 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
  { width: 15 },
];

export const downloadExcelMarketRegionActualAndExpected1 = async ({
  pmData,
  rorData,
  feeUnitData,
  costData,
  wauData,
  actualUnitsData,
  filters,
}: {
  pmData: FinacialMarketTableDataFormatedTypes[];
  rorData: FinacialMarketTableDataFormatedTypes[];
  feeUnitData: FinacialMarketTableDataFormatedTypes[];
  costData: FinacialMarketTableDataFormatedTypes[];
  wauData: FinacialMarketTableDataFormatedTypes[];
  actualUnitsData: FinacialMarketTableDataFormatedTypes[];
  filters: ReportFilters;
}) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Finance KPI Market Split YTD ');

  let displayMonth = '';
  const currentYear = filters.year || '2025';

  if (filters?.month?.length) {
    if (filters.month.length === 1) {
      displayMonth = filters.month[0];
    } else {
      displayMonth =
        filters.month[0] + ' - ' + filters.month[filters.month.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:N1');

  const titleRow = worksheet.addRow([
    'Market Split- Year To Date and Full Year',
  ]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:N2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:N3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:N4');

  worksheet.addRow(['']);
  worksheet.addRow(['']);

  const mainHeadingsRow1 = [
    '',

    'Property Management Fees',
    '',
    '',
    '',
    '',
    '',

    '', // gap

    'Return On Revenue',
    '',
    '',
    '',
    '',
    '',
  ];

  const groupHeadings = worksheet.addRow(mainHeadingsRow1);
  const lastGroupRow = groupHeadings?.number;

  worksheet.mergeCells(`B${lastGroupRow}:G${lastGroupRow}`);
  worksheet.mergeCells(`I${lastGroupRow}:N${lastGroupRow}`);

  worksheet.getCell(`B${lastGroupRow}`).alignment = alignmentCenter;
  worksheet.getCell(`I${lastGroupRow}`).alignment = alignmentCenter;
  worksheet.getCell(`B${lastGroupRow}`).font = { bold: true, size: 13 };
  worksheet.getCell(`I${lastGroupRow}`).font = { bold: true, size: 13 };

  worksheet.getCell(`B${lastGroupRow}`).border = mainHeadingCellBorders;
  worksheet.getCell(`I${lastGroupRow}`).border = mainHeadingCellBorders;

  const subGroupHeadings = [
    '',
    //Property Management Fees
    'YTD Act. Vs. Budget',
    '',
    '',

    'Full Year Exp. Vs. Budget',
    '',
    '',

    '', //gap

    //Return On Revenue
    'YTD Act. Vs. Budget',
    '',
    '',

    'Full Year Exp. Vs. Budget',
    '',
    '',
  ];
  const subGroupHeadingsRow = worksheet.addRow(subGroupHeadings);
  const lastRowSubGroup = subGroupHeadingsRow.number;

  worksheet.mergeCells(`B${lastRowSubGroup}:D${lastRowSubGroup}`);
  worksheet.mergeCells(`E${lastRowSubGroup}:G${lastRowSubGroup}`);
  worksheet.mergeCells(`I${lastRowSubGroup}:K${lastRowSubGroup}`);
  worksheet.mergeCells(`L${lastRowSubGroup}:N${lastRowSubGroup}`);
  worksheet.getCell(`B${lastRowSubGroup}`).fill = headingFill;
  worksheet.getCell(`E${lastRowSubGroup}`).fill = headingFill;
  worksheet.getCell(`I${lastRowSubGroup}`).fill = headingFill;
  worksheet.getCell(`L${lastRowSubGroup}`).fill = headingFill;
  worksheet.getCell(`B${lastRowSubGroup}`).font = headingFillMergeFont;
  worksheet.getCell(`E${lastRowSubGroup}`).font = headingFillMergeFont;
  worksheet.getCell(`I${lastRowSubGroup}`).font = headingFillMergeFont;
  worksheet.getCell(`L${lastRowSubGroup}`).font = headingFillMergeFont;
  worksheet.getCell(`B${lastRowSubGroup}`).alignment = alignmentCenter;
  worksheet.getCell(`E${lastRowSubGroup}`).alignment = alignmentCenter;
  worksheet.getCell(`I${lastRowSubGroup}`).alignment = alignmentCenter;
  worksheet.getCell(`L${lastRowSubGroup}`).alignment = alignmentCenter;
  worksheet.getCell(`B${lastRowSubGroup}`).border = mainHeadingCellBorders;
  worksheet.getCell(`E${lastRowSubGroup}`).border = mainHeadingCellBorders;
  worksheet.getCell(`I${lastRowSubGroup}`).border = mainHeadingCellBorders;
  worksheet.getCell(`L${lastRowSubGroup}`).border = mainHeadingCellBorders;

  const subHeadingsRow = [
    ' ',
    'Actuals',
    'Budget',
    'Variance',
    'Expected',
    'Budget',
    'Variance',

    '', // gap

    'Actuals',
    'Budget',
    'Variance',
    'Expected',
    'Budget',
    'Variance',
  ];

  worksheet.columns = marketTableColumsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);

  subheadings.height = rowColumnHeadingHeight;

  subheadings.eachCell((cell, colNum) => {
    cell.alignment = { wrapText: true };
    if (colNum !== 1 && colNum !== 8) {
      cell.fill = headingFill;
      cell.font = columnHeadingFillFont;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingCellBorders;
    }
  });

  // Add data rows
  for (let i = 0; i < pmData.length; i++) {
    const pm = pmData[i];
    const ror = rorData[i];

    if (pm.regionmarket !== ror.regionmarket) {
      console.log('Mi Match data');
    }

    const tableDataPmRor = worksheet.addRow([
      pm.regionmarket,
      formatValue(pm.actual, pm.unit, true),
      formatValue(pm.budget, pm.unit, true),
      formatValue(pm.variance, pm.unit, true),
      formatValue(pm.forecast, pm.unit, true),
      formatValue(pm.budget_forecast, pm.unit, true),
      formatValue(pm.variance_forecast, pm.unit, true),
      '', // gap
      formatValue(ror.actual, ror.unit, true),
      formatValue(ror.budget, ror.unit, true),
      formatValue(ror.variance, ror.unit, true),
      formatValue(ror.forecast, ror.unit, true),
      formatValue(ror.budget_forecast, ror.unit, true),
      formatValue(ror.variance_forecast, ror.unit, true),
    ]);

    tableDataPmRor.eachCell((cell, colNum) => {
      cell.alignment = { wrapText: true };

      // this is for text formting
      // PM columns: 2-7, ROR
      if (colNum >= 2 && colNum <= 7) {
        // PM columns
        if (pm.unit === '') {
          cell.numFmt = excelNumberFormateCommonSign;
        } else if (pm.unit === '%') {
          cell.numFmt = excelNumberFormatePercenatgeSign;
        } else if (pm.unit === '$') {
          cell.numFmt = excelNumberFormateDollarSign;
        }
      } else if (colNum >= 9 && colNum <= 14) {
        // ROR columns:9-14
        if (ror.unit === '') {
          cell.numFmt = excelNumberFormateCommonSign;
        } else if (ror.unit === '%') {
          cell.numFmt = excelNumberFormatePercenatgeSign;
        } else if (ror.unit === '$') {
          cell.numFmt = excelNumberFormateDollarSign;
        }
      }

      if (i % 2 === 0) {
        cell.fill = bodyRowCellFill;
        cell.border = bodyRowCellFullBorders;
      }

      // this is for border adding
      if (
        colNum === 1 ||
        colNum === 4 ||
        colNum === 7 ||
        colNum === 8 ||
        colNum === 11 ||
        colNum === 14
      ) {
        cell.border = { right: borderThin };
      }

      if (colNum !== 1 && colNum !== 8) {
        // this last row bottom border
        if (i === pmData.length - 1) {
          cell.border = {
            ...cell.border,
            bottom: { style: 'thin' },
          };
        }
        cell.alignment = alignmentRight;
      }
    });
  }

  worksheet.addRow(['']);
  worksheet.addRow(['']);

  // seconda table
  const mainHeadingsRow2 = [
    '',

    'Fee/Unit',
    '',
    '',
    '',
    '',
    '',

    '', // gap

    'Cost/Unit',
    '',
    '',
    '',
    '',
    '',
  ];

  worksheet.addRow(mainHeadingsRow2);
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:G${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `I${worksheet.lastRow?.number}:N${worksheet.lastRow?.number}`,
  );

  worksheet.getCell(`B${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;

  worksheet.getCell(`B${worksheet.lastRow?.number}`).font = {
    bold: true,
    size: 13,
  };
  worksheet.getCell(`I${worksheet.lastRow?.number}`).font = {
    bold: true,
    size: 13,
  };

  worksheet.getCell(`B${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;

  worksheet.addRow(subGroupHeadings);
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:D${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `E${worksheet.lastRow?.number}:G${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `I${worksheet.lastRow?.number}:K${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `L${worksheet.lastRow?.number}:N${worksheet.lastRow?.number}`,
  );
  worksheet.getCell(`B${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;

  worksheet.getCell(`B${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;

  worksheet.columns = marketTableColumsWidth;
  const subheadingsSecondTable = worksheet.addRow(subHeadingsRow);
  subheadingsSecondTable.height = rowColumnHeadingHeight;

  subheadingsSecondTable.eachCell((cell, colNum) => {
    cell.alignment = { wrapText: true };
    if (colNum !== 1 && colNum !== 8) {
      cell.fill = headingFill;
      cell.font = columnHeadingFillFont;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingCellBorders;
    }
  });

  // Add data rows
  for (let i = 0; i < pmData.length; i++) {
    const feeUnit = feeUnitData[i];
    const costDoor = costData[i];

    if (feeUnit.regionmarket !== costDoor.regionmarket) {
      console.log('Mi Match data');
    }

    const tableDataFeeunitCost = worksheet.addRow([
      feeUnit.regionmarket,
      formatValue(feeUnit.actual, feeUnit.unit, true),
      formatValue(feeUnit.budget, feeUnit.unit, true),
      formatValue(feeUnit.variance, feeUnit.unit, true),
      formatValue(feeUnit.forecast, feeUnit.unit, true),
      formatValue(feeUnit.budget_forecast, feeUnit.unit, true),
      formatValue(feeUnit.variance_forecast, feeUnit.unit, true),
      '', // gap
      formatValue(costDoor.actual, costDoor.unit, true),
      formatValue(costDoor.budget, costDoor.unit, true),
      formatValue(costDoor.variance, costDoor.unit, true),
      formatValue(costDoor.forecast, costDoor.unit, true),
      formatValue(costDoor.budget_forecast, costDoor.unit, true),
      formatValue(costDoor.variance_forecast, costDoor.unit, true),
    ]);

    tableDataFeeunitCost.eachCell((cell, colNum) => {
      cell.alignment = { wrapText: true };
      // this is for text formting

      if (i % 2 === 0) {
        cell.fill = bodyRowCellFill;
        cell.border = bodyRowCellFullBorders;
      }

      if (feeUnit.unit === '' && costDoor.unit === '') {
        cell.numFmt = excelNumberFormateCommonSign;
      }

      // this is for border adding
      if (
        colNum === 1 ||
        colNum === 4 ||
        colNum === 7 ||
        colNum === 8 ||
        colNum === 11 ||
        colNum === 14
      ) {
        cell.border = { right: borderThin };
      }

      if (colNum !== 1 && colNum !== 8) {
        // this last row bottom border
        if (i === pmData.length - 1) {
          cell.border = {
            ...cell.border,
            bottom: { style: 'thin' },
          };
        }
        cell.alignment = alignmentRight;
      }
    });
  }

  worksheet.addRow(['']);
  worksheet.addRow(['']);

  // third table
  const mainHeadingsRow3 = [
    '',

    'WAU',
    '',
    '',
    '',
    '',
    '',

    '', //gap

    'Actual Units',
    '',
    '',
    '',
    '',
    '',
  ];

  worksheet.addRow(mainHeadingsRow3);
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:G${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `I${worksheet.lastRow?.number}:N${worksheet.lastRow?.number}`,
  );

  worksheet.getCell(`B${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;

  worksheet.getCell(`B${worksheet.lastRow?.number}`).font = {
    bold: true,
    size: 13,
  };
  worksheet.getCell(`I${worksheet.lastRow?.number}`).font = {
    bold: true,
    size: 13,
  };

  worksheet.getCell(`B${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;

  worksheet.addRow(subGroupHeadings);
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:D${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `E${worksheet.lastRow?.number}:G${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `I${worksheet.lastRow?.number}:K${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `L${worksheet.lastRow?.number}:N${worksheet.lastRow?.number}`,
  );
  worksheet.getCell(`B${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).fill = headingFill;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).alignment =
    alignmentCenter;
  worksheet.getCell(`B${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).font =
    headingFillMergeFont;

  worksheet.getCell(`B${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`E${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`I${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;
  worksheet.getCell(`L${worksheet.lastRow?.number}`).border =
    mainHeadingCellBorders;

  worksheet.columns = marketTableColumsWidth;
  const subheadingsTable3 = worksheet.addRow(subHeadingsRow);
  subheadingsTable3.height = rowColumnHeadingHeight;

  subheadingsTable3.eachCell((cell, colNum) => {
    cell.alignment = { wrapText: true };
    if (colNum !== 1 && colNum !== 8) {
      cell.fill = headingFill;
      cell.font = columnHeadingFillFont;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingCellBorders;
    }
  });

  // Add data rows
  for (let i = 0; i < pmData.length; i++) {
    const wau = wauData[i];
    const actuaUnit = actualUnitsData[i];

    if (wau.regionmarket !== actuaUnit.regionmarket) {
      console.log('Mi Match data');
    }

    const tableData_Wau_ActualUnits = worksheet.addRow([
      wau.regionmarket,
      formatValue(wau.actual, wau.unit, true),
      formatValue(wau.budget, wau.unit, true),
      formatValue(wau.variance, wau.unit, true),
      formatValue(wau.forecast, wau.unit, true),
      formatValue(wau.budget_forecast, wau.unit, true),
      formatValue(wau.variance_forecast, wau.unit, true),
      '', // gap
      formatValue(actuaUnit.actual, actuaUnit.unit, true),
      formatValue(actuaUnit.budget, actuaUnit.unit, true),
      formatValue(actuaUnit.variance, actuaUnit.unit, true),
      formatValue(actuaUnit.forecast, actuaUnit.unit, true),
      formatValue(actuaUnit.budget_forecast, actuaUnit.unit, true),
      formatValue(actuaUnit.variance_forecast, actuaUnit.unit, true),
    ]);

    tableData_Wau_ActualUnits.eachCell((cell, colNum) => {
      cell.alignment = { wrapText: true };
      // this is for text formting
      if (wau.unit === '' && actuaUnit.unit === '') {
        cell.numFmt = excelNumberFormateCommonSign;
      }

      if (i % 2 === 0) {
        cell.fill = bodyRowCellFill;
        cell.border = bodyRowCellFullBorders;
      }

      // this is for border adding
      if (
        colNum === 1 ||
        colNum === 4 ||
        colNum === 7 ||
        colNum === 8 ||
        colNum === 11 ||
        colNum === 14
      ) {
        cell.border = { right: borderThin };
      }

      if (colNum !== 1 && colNum !== 8) {
        // this last row bottom border
        if (i === pmData.length - 1) {
          cell.border = {
            ...cell.border,
            bottom: { style: 'thin' },
          };
        }
        cell.alignment = alignmentRight;
      }
    });
  }

  const displayMonthYear = `${filters.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Market_Split_${displayMonthYear.replace(' ', '_')}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};
