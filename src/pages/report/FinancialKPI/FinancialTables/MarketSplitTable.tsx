import {
  FinanceKPIRegionActualResponse,
  FinanceKPIRegionForecastResponse,
} from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import FinancialMarketTable from '../components/FinancialMarketTable';
import { downloadExcelMarketRegionActualAndExpected1 } from '../utils/exportDownloadFormatters';

interface PropsTypes {
  actualData: FinanceKPIRegionActualResponse[];
  forecastData: FinanceKPIRegionForecastResponse[];
  filters: ReportFilters;
  datePeriod: string;
}

export interface FinacialMarketTableDataFormatedTypes {
  regionmarket: string;
  actual: number | null | undefined;
  budget: number | null | undefined;
  forecast: number | null | undefined;
  budget_forecast: number | null | undefined;
  variance: number | null | undefined;
  variance_forecast: number | null | undefined;
  unit: '' | '%' | '';
}

export default function MarketSplitTable(props: PropsTypes) {
  const { actualData, forecastData, filters, datePeriod } = props;

  const propertyManagementData = actualData?.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['PM Fees_Actual'],
      budget: actual['PM Fees_Budget'],
      variance: actual['PM Fees_Varriance'],
      forecast: forecast?.['PM Fees_Forecast'],
      budget_forecast: forecast?.['PM Fees_Budget'],
      variance_forecast: forecast?.['PM Fees_Variance'],
      unit: '' as const,
    };
  });

  const rorData = actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['ROR_Actual'],
      budget: actual['ROR_Budget'],
      variance: actual['ROR_Varriance'],
      forecast: forecast?.['ROR_Forecast'],
      budget_forecast: forecast?.['ROR_Budget'],
      variance_forecast: forecast?.['ROR_Variance'],
      unit: '%' as const,
    };
  });

  const feesUnit = actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['Fees/Unit_Actual'],
      budget: actual['Fees/Unit_Budget'],
      variance: actual['Fees/Unit_Varriance'],
      forecast: forecast?.['Fees/Unit_Forecast'],
      budget_forecast: forecast?.['Fees/Unit_Budget'],
      variance_forecast: forecast?.['Fees/Unit_Variance'],
      unit: '' as const,
    };
  });

  const costUnit = actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['Cost/Unit_Actual'],
      budget: actual['Cost/Unit_Budget'],
      variance: actual['Cost/Unit_Varriance'],
      forecast: forecast?.['Cost/Unit_Forecast'],
      budget_forecast: forecast?.['Cost/Unit_Budget'],
      variance_forecast: forecast?.['Cost/Unit_Variance'],
      unit: '' as const,
    };
  });

  const wauData = actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['WAU_Actual'],
      budget: actual['WAU_Budget'],
      variance: actual['WAU_Varriance'],
      forecast: forecast?.['WAU_Forecast'],
      budget_forecast: forecast?.['WAU_Budget'],
      variance_forecast: forecast?.['WAU_Variance'],
      unit: '' as const,
    };
  });

  const actualUnitsData = actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['Actual Units_Actual'],
      budget: actual['Actual Units_Budget'],
      variance: actual['Actual Units_Varriance'],
      forecast: forecast?.['Actual Units_Forecast'],
      budget_forecast: forecast?.['Actual Units_Budget'],
      variance_forecast: forecast?.['Actual Units_Variance'],
      unit: '' as const,
    };
  });

  return (
    <>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>

        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF] "
          onClick={() =>
            downloadExcelMarketRegionActualAndExpected1({
              pmData: propertyManagementData,
              rorData: rorData,
              feeUnitData: feesUnit,
              costData: costUnit,
              wauData: wauData,
              actualUnitsData: actualUnitsData,
              filters: filters,
            })
          }
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <FinancialMarketTable
        tableHeading1="Property Management Fees"
        tableData1={propertyManagementData}
        tableHeading2="Return On Revenue"
        tableData2={rorData}
      />
      <div className="my-6">
        <FinancialMarketTable
          tableHeading1="Fee/Unit"
          tableData1={feesUnit}
          tableHeading2="Cost/Unit"
          tableData2={costUnit}
        />
      </div>

      <FinancialMarketTable
        tableHeading1="WAU"
        tableData1={wauData}
        tableHeading2="Actual Units"
        tableData2={actualUnitsData}
      />
    </>
  );
}
