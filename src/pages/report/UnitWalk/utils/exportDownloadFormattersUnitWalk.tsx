import {
  UnitWalkKPIRegionTypes,
  UnitWalkKPIYTDMarketTypes,
} from '@/api/unitWalkApis/unitWalkTypes';
import {
  alignmentCenter,
  alignmentLeft,
  alignmentRight,
  bodyRowCellFill,
  bodyRowCellFullBorders,
  columnHeadingFillFont,
  companyNameRowFont,
  excelLightBlueColor,
  excelPurpleColor,
  filterConsolidationRowFont,
  filterPeriodRowFont,
  mainHeadingCellBorders,
  reportTitleRowFont,
  rowColumnHeadingHeight,
} from '@/constants/exportExcelStyles';
import { getConsolidationHeader } from '@/helpers/exportExcelGetConsolidationHeader';
import { ReportFilters } from '@/slice/incomeReportSlice';
import ExcelJS from 'exceljs';
import { sumUnitWalkRegionColumns } from './unitWalkHelpers';

const purpleColor = { argb: 'FF43298F' };

const headingFill = {
  type: 'pattern' as const,
  pattern: 'solid' as const,
  fgColor: purpleColor,
};

export const downloadExcelUnitWalkRegion = async (
  unitWalkRegionData: UnitWalkKPIRegionTypes[],
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Unit Walk Region');

  let displayMonth = '';
  const currentYear = filters?.year || '2025';

  if (filters?.month?.length) {
    if (filters.month?.length === 1) {
      displayMonth = filters?.month[0];
    } else {
      displayMonth =
        filters?.month[0] + ' - ' + filters?.month[filters?.month?.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:E1');

  const titleRow = worksheet.addRow([`Unit Walk Region `]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:E2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:E3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:E4');

  worksheet.addRow('');
  worksheet.addRow('');
  const subHeadingsRow = [
    'Region',
    'Beginning',
    'Additions',
    'Losses',
    'Current',
  ];

  const subheadings = worksheet.addRow(subHeadingsRow);

  subheadings.height = rowColumnHeadingHeight;

  worksheet.columns = [
    { width: 14 },
    { width: 12 },
    { width: 12 },
    { width: 12 },
    { width: 12 },
  ];

  subheadings.eachCell((cell) => {
    cell.fill = headingFill;
    cell.font = columnHeadingFillFont;
    cell.border = mainHeadingCellBorders;
    cell.alignment = alignmentCenter;
  });

  unitWalkRegionData?.forEach(
    ({ Region, beginning, additions, losses, current }, index) => {
      const rowCells = worksheet.addRow([
        Region,
        beginning,
        additions,
        losses,
        current,
      ]);

      rowCells.eachCell((cell, colNum) => {
        if (colNum !== 1) {
          cell.alignment = alignmentRight;
        } else {
          cell.alignment = alignmentLeft;
        }

        if (index % 2 === 0) {
          cell.fill = bodyRowCellFill;
          cell.border = bodyRowCellFullBorders;
        }
        cell.numFmt = '#,##0;(#,##0)';
      });
    },
  );

  const columns = ['beginning', 'additions', 'losses', 'current'] as const;
  const totals = sumUnitWalkRegionColumns(unitWalkRegionData, [...columns]);
  const totalRow = worksheet.addRow([
    'Total',
    totals.beginning,
    totals.additions,
    totals.losses,
    totals.current,
  ]);

  totalRow.eachCell((cell) => {
    cell.font = { bold: true, color: excelPurpleColor };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: excelLightBlueColor,
    };
    cell.numFmt = '#,##0;(#,##0)';
  });

  const displayMonthYear = `${filters?.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Unit_Walk_Region_${displayMonthYear}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

export const downloadExcelUnitWalkYTDMarket = async (
  unitWalkYTDMarket: UnitWalkKPIYTDMarketTypes[],
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Unit Walk YTD Market');

  let displayMonth = '';
  const currentYear = filters?.year || '2025';

  if (filters?.month?.length) {
    if (filters.month?.length === 1) {
      displayMonth = filters?.month[0];
    } else {
      displayMonth =
        filters?.month[0] + ' - ' + filters?.month[filters?.month?.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:E1');

  const titleRow = worksheet.addRow([`YTD Unit Walk Market`]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:E2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);

  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:E3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:E4');

  worksheet.addRow('');
  worksheet.addRow('');
  const subHeadingsRow = [
    'Market',
    'Beginning',
    'Additions',
    'Losses',
    'Current',
  ];
  // worksheet.columns = columsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);

  subheadings.height = rowColumnHeadingHeight;

  worksheet.columns = [
    { width: 14 },
    { width: 12 },
    { width: 12 },
    { width: 12 },
    { width: 12 },
  ];

  subheadings.eachCell((cell) => {
    cell.fill = headingFill;
    cell.font = columnHeadingFillFont;
    cell.border = mainHeadingCellBorders;
    cell.alignment = alignmentCenter;
  });

  unitWalkYTDMarket?.forEach(
    ({ RegionMarket, beginning, additions, losses, current }, index) => {
      const rowCells = worksheet.addRow([
        RegionMarket,
        beginning,
        additions,
        losses,
        current,
      ]);

      rowCells.eachCell((cell, colNum) => {
        if (colNum !== 1) {
          cell.alignment = alignmentRight;
        } else {
          cell.alignment = alignmentLeft;
        }

        if (index % 2 === 0) {
          cell.fill = bodyRowCellFill;
          cell.border = bodyRowCellFullBorders;
        }
        cell.numFmt = '#,##0;(#,##0)';
      });
    },
  );

  const displayMonthYear = `${filters?.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `unit_walk_YTD_Market_${displayMonthYear}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};
