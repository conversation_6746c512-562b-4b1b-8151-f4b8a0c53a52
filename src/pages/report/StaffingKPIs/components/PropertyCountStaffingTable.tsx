import { StaffingRPMKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableHeadingCell,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingPropertyCount } from '../utils/exportDownloadFormattersStaffingKPI';

interface PropsTypes {
  rpmData: StaffingRPMKPITypes[];
  datePeriod: string;
  filters: ReportFilters;
}

const propertyCountTableColumWidth = 'w-[130px]';

export default function PropertyCountStaffingTable(props: PropsTypes) {
  const { rpmData, datePeriod, filters } = props;
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() => downloadExcelStaffingPropertyCount(rpmData, filters)}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <CommonTable>
        <thead>
          <CommonTableSubHeaderRow>
            <th className={`${propertyCountTableColumWidth} text-start`}>
              RPM Name
            </th>
            <CommonTableHeadingCell
              borderLeft
              className={`${propertyCountTableColumWidth}`}
            >
              Stabilized
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${propertyCountTableColumWidth}`}
            >
              Lease-Up
            </CommonTableHeadingCell>

            <CommonTableHeadingCell
              className={`${propertyCountTableColumWidth}`}
            >
              Total Properties
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              borderRight
              className={`${propertyCountTableColumWidth}`}
            >
              Expected PM Fees
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>
        <tbody>
          {rpmData?.map((item) => {
            return (
              <CommonTableBodyRow key={item?.RPM}>
                <td className="text-left">{item?.RPM}</td>

                <CommonTableBodyCell borderLeft>
                  {item?.Stabilised}
                </CommonTableBodyCell>
                <CommonTableBodyCell>{item?.Lease_Up}</CommonTableBodyCell>

                <CommonTableBodyCell>
                  {item?.Total_Properties}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {/* {item?.Expected_PM_Fees?.toLocaleString('en-US', {maximumFractionDigits:1})} */}
                  {item?.Expected_PM_Fees &&
                    Math.round(item?.Expected_PM_Fees)?.toLocaleString('en-US')}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
        </tbody>
      </CommonTable>
    </div>
  );
}
