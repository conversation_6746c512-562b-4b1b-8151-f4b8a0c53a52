import { useEffect } from 'react';
import {
  getPropertyManagementJTurner,
  getPropertyManagementNOI,
  getPropertyManagementYoY,
  PropertyManagementKpiPayload,
  PropertyManagementKpiResponse,
} from '@/api/propertyManagementKpiApi';
import {
  OccupancyRentsItem,
  OperationalKpiItem,
  PropertyPerformanceItem,
  setOccupancyRentsData,
  setOperationalKpisData,
  setPropertyPerformanceData,
  setSectionError,
  setSectionLoading,
} from '@/slice/propertyManagementKpiSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

const getMonthNumber = (monthName: string): string => {
  const months = {
    January: '1',
    February: '2',
    March: '3',
    April: '4',
    May: '5',
    June: '6',
    July: '7',
    August: '8',
    September: '9',
    October: '10',
    November: '11',
    December: '12',
  };
  return months[monthName as keyof typeof months] || '1';
};

const transformOccupancyRentsData = (
  apiData: PropertyManagementKpiResponse[],
): OccupancyRentsItem[] => {
  return apiData.map((item) => ({
    category: item.parameter,
    central: {
      actualVsBudget: `${item.Central.toFixed(2)}%`,
      actualVsSubmarket: `${item.Central.toFixed(2)}%`, // Using same value for now
    },
    east: {
      actualVsBudget: `${item.East.toFixed(2)}%`,
      actualVsSubmarket: `${item.East.toFixed(2)}%`,
    },
    west: {
      actualVsBudget: `${item.West.toFixed(2)}%`,
      actualVsSubmarket: `${item.West.toFixed(2)}%`,
    },
    consolidated: {
      actualVsBudget: `${item.Consolidated.toFixed(2)}%`,
      actualVsMarket: `${item.Consolidated.toFixed(2)}%`,
    },
  }));
};

const transformOperationalKpisData = (
  apiData: PropertyManagementKpiResponse[],
): OperationalKpiItem[] => {
  return apiData.map((item) => ({
    category: item.parameter,
    central: {
      actual: item.Central.toFixed(6),
      target: (item.Central * 0.95).toFixed(6), // Using 95% of actual as target placeholder
    },
    east: {
      actual: item.East.toFixed(6),
      target: (item.East * 0.95).toFixed(6),
    },
    west: {
      actual: item.West.toFixed(6),
      target: (item.West * 0.95).toFixed(6),
    },
    consolidated: {
      actual: item.Consolidated.toFixed(6),
      target: (item.Consolidated * 0.95).toFixed(6),
    },
  }));
};

const transformPropertyPerformanceData = (
  apiData: PropertyManagementKpiResponse[],
): PropertyPerformanceItem[] => {
  return apiData.map((item) => ({
    lineItem: item.parameter,
    central: `${item.Central.toFixed(2)}%`,
    east: `${item.East.toFixed(2)}%`,
    west: `${item.West.toFixed(2)}%`,
    consolidated: `${item.Consolidated.toFixed(2)}%`,
  }));
};

export const usePropertyManagementKpiData = () => {
  const dispatch = useDispatch();
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const createRequestPayload = (): PropertyManagementKpiPayload => {
    const monthNumbers =
      filters.month.length > 0
        ? filters.month.map((month) => getMonthNumber(month))
        : ['1'];

    return {
      year: filters.year || '2025',
      month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
      department:
        filters.department.length === 0
          ? null
          : filters.department.length === 1
            ? filters.department[0]
            : filters.department,
      businessType:
        filters.businessType.length === 0
          ? null
          : filters.businessType.length === 1
            ? filters.businessType[0]
            : filters.businessType,
      marketleader:
        filters.marketLeader.length === 0
          ? null
          : filters.marketLeader.length === 1
            ? filters.marketLeader[0]
            : filters.marketLeader,
      adminBu:
        filters.adminBU.length === 0
          ? null
          : filters.adminBU.length === 1
            ? filters.adminBU[0]
            : filters.adminBU,
    };
  };

  const fetchOccupancyRentsData = async (
    payload: PropertyManagementKpiPayload,
  ) => {
    dispatch(setSectionLoading({ occupancyRents: true }));
    dispatch(setSectionError({ occupancyRents: null }));

    try {
      const response = await getPropertyManagementYoY(payload);
      if (response && response.data) {
        if (response.data.length === 0) {
          toast.warning(
            'No occupancy & rents data found for the selected filters',
          );
        }
        const transformedData = transformOccupancyRentsData(response.data);
        dispatch(setOccupancyRentsData(transformedData));
      }
    } catch (error) {
      console.error('Error fetching occupancy rents data:', error);
      dispatch(
        setSectionError({
          occupancyRents: 'Failed to fetch occupancy & rents data',
        }),
      );
    } finally {
      dispatch(setSectionLoading({ occupancyRents: false }));
    }
  };

  const fetchOperationalKpisData = async (
    payload: PropertyManagementKpiPayload,
  ) => {
    dispatch(setSectionLoading({ operationalKpis: true }));
    dispatch(setSectionError({ operationalKpis: null }));

    try {
      const response = await getPropertyManagementNOI(payload);
      if (response && response.data) {
        if (response.data.length === 0) {
          toast.warning(
            'No operational KPIs data found for the selected filters',
          );
        }
        const transformedData = transformOperationalKpisData(response.data);
        dispatch(setOperationalKpisData(transformedData));
      }
    } catch (error) {
      console.error('Error fetching operational KPIs data:', error);
      dispatch(
        setSectionError({
          operationalKpis: 'Failed to fetch operational KPIs data',
        }),
      );
    } finally {
      dispatch(setSectionLoading({ operationalKpis: false }));
    }
  };

  const fetchPropertyPerformanceData = async (
    payload: PropertyManagementKpiPayload,
  ) => {
    dispatch(setSectionLoading({ propertyPerformance: true }));
    dispatch(setSectionError({ propertyPerformance: null }));

    try {
      const response = await getPropertyManagementJTurner(payload);
      if (response && response.data) {
        if (response.data.length === 0) {
          toast.warning(
            'No property performance data found for the selected filters',
          );
        }
        const transformedData = transformPropertyPerformanceData(response.data);
        dispatch(setPropertyPerformanceData(transformedData));
      }
    } catch (error) {
      console.error('Error fetching property performance data:', error);
      dispatch(
        setSectionError({
          propertyPerformance: 'Failed to fetch property performance data',
        }),
      );
    } finally {
      dispatch(setSectionLoading({ propertyPerformance: false }));
    }
  };

  const fetchAllData = async () => {
    const payload = createRequestPayload();

    // Fetch all data in parallel for better performance
    await Promise.all([
      fetchOccupancyRentsData(payload),
      fetchOperationalKpisData(payload),
      fetchPropertyPerformanceData(payload),
    ]);
  };

  useEffect(() => {
    fetchAllData();
  }, [filters, dispatch]);

  return {
    fetchAllData,
    fetchOccupancyRentsData: () =>
      fetchOccupancyRentsData(createRequestPayload()),
    fetchOperationalKpisData: () =>
      fetchOperationalKpisData(createRequestPayload()),
    fetchPropertyPerformanceData: () =>
      fetchPropertyPerformanceData(createRequestPayload()),
  };
};
