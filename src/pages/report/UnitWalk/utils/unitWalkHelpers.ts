import { UnitWalkKPIRegionTypes } from '@/api/unitWalkApis/unitWalkTypes';

export function sumUnitWalkRegionColumns(
  data: UnitWalkKPIRegionTypes[],
  keys: ReadonlyArray<keyof UnitWalkKPIRegionTypes>
): Record<keyof UnitWalkKPIRegionTypes, number> {
  const totals = {} as Record<keyof UnitWalkKPIRegionTypes, number>;

  keys.forEach((key) => {
    totals[key] = data.reduce((sum, item) => {
      const value = item[key];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
  });
  return totals;
}